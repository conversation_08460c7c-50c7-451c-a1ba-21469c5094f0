{"name": "wrap-ansi", "version": "6.2.0", "description": "Wordwrap a string with ANSI escape codes", "license": "MIT", "repository": "chalk/wrap-ansi", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && nyc ava"}, "files": ["index.js"], "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "devDependencies": {"ava": "^2.1.0", "chalk": "^2.4.2", "coveralls": "^3.0.3", "has-ansi": "^3.0.0", "nyc": "^14.1.1", "xo": "^0.24.0"}}