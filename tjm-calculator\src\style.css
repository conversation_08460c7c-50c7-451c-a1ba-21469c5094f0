@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a, a:hover, a:after{
  font-weight: 500;
  color: inherit;
  text-decoration: inherit;
}
nav li:hover {
  color: #535bf2;
}

/* body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
} */

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

/* button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
} */

.card {
  padding: 2em;
}

/* #app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
} */

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a, a:hover, a:after{
    font-weight: 500;
    color: inherit;
    text-decoration: inherit;
  }
  nav li:hover {
    color: #535bf2;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* Styles spécifiques pour le calculateur TJM */
section {
  display: flex;
  flex-direction: column;
}

section > div {
  flex: 1;
}

.auto-rows-auto {
  grid-auto-rows: auto;
  /* Remplace auto-rows-fr pour adapter la hauteur au contenu */
}

/* Style spécifique pour les sections avec peu de contenu */
.content-fit {
  height: auto;
  min-height: fit-content;
}

/* Styles pour l'animation de collapse/expand */
.expand-transition {
  transition: max-height 0.3s ease-out;
  overflow: hidden;
}

.collapsed {
  max-height: 0;
}

.expanded {
  max-height: 1000px;
  /* Valeur suffisamment grande pour contenir tout le contenu */
}

.peer:checked ~ .peer-checked\:after\:translate-x-full::after {
  transform: inherit !important;
}
