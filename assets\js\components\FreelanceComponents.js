// Composants pour les développeurs freelances

export const FreelanceBasics = {
    template: `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                📋 Section 1 : Informations de base
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                Type de projet, technologies, deadline, nombre de pages...
            </p>
            
            <!-- Contenu à développer selon docs/contexte.md -->
            <div class="space-y-4">
                <!-- Type de projet -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Type de projet
                    </label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option>Site vitrine</option>
                        <option>SaaS</option>
                        <option>E-commerce</option>
                        <option>API</option>
                        <option>App mobile</option>
                        <option>Dashboard</option>
                        <option>Autre</option>
                    </select>
                </div>
                
                <!-- Technologies -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Technologies à utiliser
                    </label>
                    <input type="text" placeholder="Ex: PHP, Vue.js, MySQL" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                </div>
                
                <!-- Deadline -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Deadline (nombre de jours)
                    </label>
                    <input type="number" placeholder="Ex: 30" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                </div>
                
                <!-- Nombre de pages -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Nombre de pages
                    </label>
                    <input type="number" placeholder="Ex: 5" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                </div>
            </div>
            
            <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                <p class="text-sm text-blue-700 dark:text-blue-300">
                    🚧 Composant FreelanceBasics - Structure de base créée, à compléter
                </p>
            </div>
        </div>
    `
};

export const FreelanceConstraints = {
    template: `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                ⚙️ Section 2 : Contraintes du freelance
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                Niveau de compétence, temps plein, TJM cible, marge de sécurité...
            </p>
            
            <!-- Contenu à développer -->
            <div class="space-y-4">
                <!-- Niveau de compétence -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Niveau de compétence sur les technologies
                    </label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option>Débutant</option>
                        <option>Intermédiaire</option>
                        <option>Expert</option>
                    </select>
                </div>
                
                <!-- Temps plein -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Travail à temps plein sur le projet ?
                    </label>
                    <div class="flex space-x-4">
                        <label class="flex items-center">
                            <input type="radio" name="temps_plein" value="oui" class="mr-2">
                            <span class="text-gray-700 dark:text-gray-300">Oui</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="temps_plein" value="non" class="mr-2">
                            <span class="text-gray-700 dark:text-gray-300">Non</span>
                        </label>
                    </div>
                </div>
                
                <!-- Marge de sécurité -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Marge de sécurité
                    </label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option>0%</option>
                        <option>10%</option>
                        <option>20%</option>
                        <option>30%</option>
                    </select>
                </div>
            </div>
            
            <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                <p class="text-sm text-blue-700 dark:text-blue-300">
                    🚧 Composant FreelanceConstraints - Structure de base créée, à compléter
                </p>
            </div>
        </div>
    `
};

export const FreelanceFeatures = {
    template: `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                🔧 Section 3 : Fonctionnalités additionnelles
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                Authentification, tableau de bord, API externes, paiement...
            </p>
            
            <!-- Liste des fonctionnalités selon docs/contexte.md -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Authentification / Espace membre</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Tableau de bord / back-office</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Intégration API externe</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Paiement en ligne</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Système de recherche / filtres</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Messagerie / notifications</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Admin CRUD complet</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">CMS (contenu dynamique)</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Responsive mobile</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Tests automatisés</span>
                </label>
            </div>
            
            <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                <p class="text-sm text-blue-700 dark:text-blue-300">
                    🚧 Composant FreelanceFeatures - Structure de base créée, à compléter
                </p>
            </div>
        </div>
    `
};

export const FreelanceDeliverables = {
    template: `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                📦 Section 4 : Livrable & périmètre attendu
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
                Dev seul ou + UI/UX, maquettes, réunions, hébergement...
            </p>
            <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                <p class="text-sm text-blue-700 dark:text-blue-300">
                    🚧 Composant FreelanceDeliverables - À développer
                </p>
            </div>
        </div>
    `
};

export const FreelanceObjectives = {
    template: `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                🎯 Section 5 : Objectif personnel
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
                Rentabilité, portfolio, progression, client stratégique...
            </p>
            <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                <p class="text-sm text-blue-700 dark:text-blue-300">
                    🚧 Composant FreelanceObjectives - À développer
                </p>
            </div>
        </div>
    `
};
