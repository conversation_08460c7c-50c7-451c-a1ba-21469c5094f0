<?php

// Autoloader simple pour le développement
spl_autoload_register(function ($class) {
    // Conversion du namespace en chemin de fichier
    $file = __DIR__ . '/../src/' . str_replace(['App\\', '\\'], ['', '/'], $class) . '.php';
    
    if (file_exists($file)) {
        require_once $file;
        return true;
    }
    
    return false;
});

// Chargement des classes Symfony essentielles (simulation)
if (!class_exists('Symfony\Component\HttpFoundation\Request')) {
    // Simulation basique des classes Symfony pour le développement
    require_once __DIR__ . '/symfony_mock.php';
}
