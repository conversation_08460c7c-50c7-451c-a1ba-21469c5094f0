// Composants pour les entreprises

export const BusinessBasics = {
    template: `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                📋 Section 1 : Informations de base
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                Type de projet, technologies, deadline, pourquoi chiffrer ce projet...
            </p>
            
            <!-- Contenu à développer selon docs/contexte.md -->
            <div class="space-y-4">
                <!-- Type de projet -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Type de projet
                    </label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option>Site vitrine</option>
                        <option>E-commerce</option>
                        <option>SaaS</option>
                        <option>Portail B2B</option>
                        <option>Application mobile</option>
                        <option>Back-office</option>
                    </select>
                </div>
                
                <!-- Projet existant ou nouveau -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Type de projet
                    </label>
                    <div class="flex space-x-4">
                        <label class="flex items-center">
                            <input type="radio" name="projet_type" value="nouveau" class="mr-2">
                            <span class="text-gray-700 dark:text-gray-300">From scratch</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="projet_type" value="existant" class="mr-2">
                            <span class="text-gray-700 dark:text-gray-300">Existant à améliorer</span>
                        </label>
                    </div>
                </div>
                
                <!-- Nombre de pages -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Nombre de pages ou écrans
                    </label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option>1-5</option>
                        <option>6-10</option>
                        <option>10-20</option>
                        <option>20+</option>
                    </select>
                </div>
                
                <!-- Pourquoi chiffrer -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Pourquoi souhaitez-vous chiffrer ce projet ?
                    </label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="radio" name="raison_chiffrage" value="facturer" class="mr-2">
                            <span class="text-gray-700 dark:text-gray-300">🧑‍💻 Pour le facturer à un client</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="raison_chiffrage" value="budget_interne" class="mr-2">
                            <span class="text-gray-700 dark:text-gray-300">🚀 Pour budgéter un projet interne</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="raison_chiffrage" value="roadmap" class="mr-2">
                            <span class="text-gray-700 dark:text-gray-300">🏢 Pour prioriser dans une roadmap</span>
                        </label>
                        <label class="flex items-center">
                            <input type="radio" name="raison_chiffrage" value="faisabilite" class="mr-2">
                            <span class="text-gray-700 dark:text-gray-300">📈 Pour évaluer la faisabilité</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="mt-4 p-4 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-200 dark:border-purple-800">
                <p class="text-sm text-purple-700 dark:text-purple-300">
                    🚧 Composant BusinessBasics - Structure de base créée, à compléter
                </p>
            </div>
        </div>
    `
};

export const BusinessStructure = {
    template: `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                🏗️ Section 2 : Structure & organisation de l'entreprise
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                Rôle, équipe projet, méthodologie, nombre de personnes...
            </p>
            
            <div class="space-y-4">
                <!-- Rôle -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Votre rôle
                    </label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option>CTO</option>
                        <option>Chef de projet</option>
                        <option>Développeur</option>
                        <option>CMO</option>
                        <option>Fondateur</option>
                        <option>Autre</option>
                    </select>
                </div>
                
                <!-- Qui travaille sur le projet -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Qui travaille sur ce projet ?
                    </label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2">
                            <span class="text-gray-700 dark:text-gray-300">Développeurs internes</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2">
                            <span class="text-gray-700 dark:text-gray-300">Freelances externes</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2">
                            <span class="text-gray-700 dark:text-gray-300">Agence ou sous-traitant</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="mr-2">
                            <span class="text-gray-700 dark:text-gray-300">Mix interne / externe</span>
                        </label>
                    </div>
                </div>
                
                <!-- Méthodologie -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Méthodologie prévue
                    </label>
                    <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        <option>Agile</option>
                        <option>Scrum</option>
                        <option>Waterfall</option>
                        <option>Pas encore défini</option>
                    </select>
                </div>
            </div>
            
            <div class="mt-4 p-4 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-200 dark:border-purple-800">
                <p class="text-sm text-purple-700 dark:text-purple-300">
                    🚧 Composant BusinessStructure - Structure de base créée, à compléter
                </p>
            </div>
        </div>
    `
};

export const BusinessFeatures = {
    template: `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                🔧 Section 3 : Fonctionnalités et périmètre fonctionnel
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                SSO, API, e-commerce, scalabilité, complexité fonctionnelle...
            </p>
            
            <!-- Fonctionnalités principales selon docs/contexte.md -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Authentification / SSO</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Tableau de bord</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">API à connecter ou créer</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">E-commerce</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Admin CRUD complet</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Notifications / emails</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Gestion de rôles / permissions</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Système de recherche avancée</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Intégration ERP / CRM</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Multilingue</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">Tests automatisés / CI</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="mr-3">
                    <span class="text-gray-700 dark:text-gray-300">RGPD / sécurité renforcée</span>
                </label>
            </div>
            
            <!-- Complexité -->
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Degré de complexité fonctionnelle estimé
                </label>
                <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                    <option>Basique</option>
                    <option>Modéré</option>
                    <option>Complexe</option>
                    <option>Très complexe</option>
                </select>
            </div>
            
            <div class="mt-4 p-4 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-200 dark:border-purple-800">
                <p class="text-sm text-purple-700 dark:text-purple-300">
                    🚧 Composant BusinessFeatures - Structure de base créée, à compléter
                </p>
            </div>
        </div>
    `
};

export const BusinessDeliverables = {
    template: `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                📦 Section 4 : Livrable attendu & périmètre
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
                UI/UX, maquettes, spécifications, déploiement, monitoring...
            </p>
            <div class="mt-4 p-4 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-200 dark:border-purple-800">
                <p class="text-sm text-purple-700 dark:text-purple-300">
                    🚧 Composant BusinessDeliverables - À développer
                </p>
            </div>
        </div>
    `
};

export const BusinessObjectives = {
    template: `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                🎯 Section 5 : Objectifs business du projet
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
                MVP, production, budget, urgence du projet...
            </p>
            <div class="mt-4 p-4 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-200 dark:border-purple-800">
                <p class="text-sm text-purple-700 dark:text-purple-300">
                    🚧 Composant BusinessObjectives - À développer
                </p>
            </div>
        </div>
    `
};

export const BusinessPricing = {
    template: `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                💰 Section 6 : Structure tarifaire
            </h3>
            <p class="text-gray-600 dark:text-gray-400">
                Coût journalier par profil, marge, forfait vs régie...
            </p>
            <div class="mt-4 p-4 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-200 dark:border-purple-800">
                <p class="text-sm text-purple-700 dark:text-purple-300">
                    🚧 Composant BusinessPricing - À développer
                </p>
            </div>
        </div>
    `
};
