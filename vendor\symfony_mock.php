<?php

// Mock simple des classes Symfony pour le développement

namespace Symfony\Component\HttpFoundation {
    class Request {
        public static function createFromGlobals() {
            return new self();
        }
        
        public function getContent() {
            return file_get_contents('php://input');
        }
    }
    
    class Response {
        private $content;
        private $statusCode;
        
        public function __construct($content = '', $status = 200) {
            $this->content = $content;
            $this->statusCode = $status;
        }
        
        public function send() {
            http_response_code($this->statusCode);
            echo $this->content;
        }
    }
    
    class JsonResponse extends Response {
        public function __construct($data = null, $status = 200) {
            parent::__construct(json_encode($data), $status);
            header('Content-Type: application/json');
        }
    }
}

namespace Symfony\Bundle\FrameworkBundle\Controller {
    abstract class AbstractController {
        protected function render($template, $parameters = []) {
            // Simulation du rendu Twig
            $templatePath = __DIR__ . '/../templates/' . $template;
            if (file_exists($templatePath)) {
                extract($parameters);
                ob_start();
                include $templatePath;
                $content = ob_get_clean();
                return new \Symfony\Component\HttpFoundation\Response($content);
            }
            return new \Symfony\Component\HttpFoundation\Response('Template not found: ' . $template, 404);
        }
        
        protected function json($data, $status = 200) {
            return new \Symfony\Component\HttpFoundation\JsonResponse($data, $status);
        }
    }
}

namespace Symfony\Component\Routing\Annotation {
    class Route {
        public function __construct($path, $name = null, $methods = []) {
            // Mock pour les annotations de route
        }
    }
}
