<?php

// Mock simple des classes Symfony pour le développement

namespace Symfony\Component\HttpFoundation {
    class Request {
        public static function createFromGlobals() {
            return new self();
        }

        public function getContent() {
            return file_get_contents('php://input');
        }

        public function getPathInfo() {
            return parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        }
    }
    
    class Response {
        private $content;
        private $statusCode;
        
        public function __construct($content = '', $status = 200) {
            $this->content = $content;
            $this->statusCode = $status;
        }
        
        public function send() {
            http_response_code($this->statusCode);
            echo $this->content;
        }
    }
    
    class JsonResponse extends Response {
        public function __construct($data = null, $status = 200) {
            parent::__construct(json_encode($data), $status);
            header('Content-Type: application/json');
        }
    }
}

namespace Symfony\Bundle\FrameworkBundle\Controller {
    abstract class AbstractController {
        protected function render($template, $parameters = []) {
            // Rendu direct du HTML pour l'estimation
            if ($template === 'estimation/index.html.twig') {
                $content = $this->renderEstimationTemplate($parameters);
                return new \Symfony\Component\HttpFoundation\Response($content);
            }
            return new \Symfony\Component\HttpFoundation\Response('Template not found: ' . $template, 404);
        }

        private function renderEstimationTemplate($parameters) {
            return '<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <title>QuickEsti - Estimation de projet</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="/build/app.94f78fc5.css" rel="stylesheet">
    </head>
    <body class="bg-gray-50 dark:bg-gray-900">
        <div class="bg-gray-100 dark:bg-gray-900 min-h-screen">
            <!-- Header -->
            <header class="bg-white dark:bg-gray-800 shadow">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                        QuickEsti 💼📊
                    </h1>
                    <p class="mt-2 text-lg text-gray-600 dark:text-gray-400">
                        Estimez le coût et la durée de votre projet web en quelques clics
                    </p>
                </div>
            </header>

            <!-- Main Content -->
            <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div class="px-4 py-6 sm:px-0">
                    <!-- Vue.js App Container -->
                    <div id="estimation-app">
                        <!-- Le contenu Vue.js sera injecté ici -->
                        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                            <div class="px-4 py-5 sm:p-6">
                                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                                    Configuration de l\'estimation
                                </h2>

                                <!-- Composant Vue de test -->
                                <test-component></test-component>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- Vue.js via CDN -->
        <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
        <!-- Flowbite -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.0/flowbite.min.js"></script>

        <!-- Application Vue.js -->
        <script>
        const { createApp } = Vue;

        // Composant de test
        const TestComponent = {
            template: `
                <div class="p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                        🎉 Vue.js est connecté !
                    </h3>
                    <p class="text-blue-700 dark:text-blue-300 mb-4">
                        Compteur: {{ count }}
                    </p>
                    <button
                        @click="increment"
                        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200"
                    >
                        Incrémenter
                    </button>
                </div>
            `,
            data() {
                return {
                    count: 0
                }
            },
            methods: {
                increment() {
                    this.count++;
                }
            }
        };

        // Application Vue.js
        const app = createApp({
            components: {
                \'test-component\': TestComponent
            }
        });

        app.mount(\'#estimation-app\');
        </script>

        <script defer src="/build/app.cabe8d39.js"></script>
    </body>
</html>';
        }
        
        protected function json($data, $status = 200) {
            return new \Symfony\Component\HttpFoundation\JsonResponse($data, $status);
        }
    }
}

namespace Symfony\Component\Routing\Annotation {
    class Route {
        public function __construct($path, $name = null, $methods = []) {
            // Mock pour les annotations de route
        }
    }
}
