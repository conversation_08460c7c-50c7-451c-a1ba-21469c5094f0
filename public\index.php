<?php

// Autoloader
require_once dirname(__DIR__).'/vendor/autoload.php';

use App\Controller\EstimationController;
use App\Controller\ApiController;
use Symfony\Component\HttpFoundation\Request;

// Création de la requête
$request = Request::createFromGlobals();
$path = $request->getPathInfo() ?: '/';

// Routing simple
try {
    switch ($path) {
        case '/':
            echo '<h1>QuickEsti</h1><p><a href="/estimation">Aller à l\'estimation</a></p>';
            break;
            
        case '/estimation':
            $controller = new EstimationController();
            $response = $controller->index();
            $response->send();
            break;
            
        case '/api/estimation':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $controller = new ApiController();
                $response = $controller->estimation($request);
                $response->send();
            } else {
                http_response_code(405);
                echo 'Method Not Allowed';
            }
            break;
            
        default:
            http_response_code(404);
            echo '404 - Page not found';
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo 'Error: ' . $e->getMessage();
}
