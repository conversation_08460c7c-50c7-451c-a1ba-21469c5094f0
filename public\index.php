<?php

// Routing simple basé sur l'URL
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Routing simple
switch ($path) {
    case '/':
        echo '<h1>QuickEsti</h1><p><a href="/estimation">Aller à l\'estimation</a></p>';
        break;

    case '/estimation':
        // Rendu direct de la page d'estimation
        echo '<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <title>QuickEsti - Estimation de projet</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="/build/app.94f78fc5.css" rel="stylesheet">
    </head>
    <body class="bg-gray-50 dark:bg-gray-900">
        <div class="bg-gray-100 dark:bg-gray-900 min-h-screen">
            <!-- Header -->
            <header class="bg-white dark:bg-gray-800 shadow">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                        QuickEsti 💼📊
                    </h1>
                    <p class="mt-2 text-lg text-gray-600 dark:text-gray-400">
                        Estimez le coût et la durée de votre projet web en quelques clics
                    </p>
                </div>
            </header>

            <!-- Main Content -->
            <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div class="px-4 py-6 sm:px-0">
                    <!-- Vue.js App Container -->
                    <div id="estimation-app">
                        <!-- Le contenu Vue.js sera injecté ici -->
                        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                            <div class="px-4 py-5 sm:p-6">
                                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                                    Configuration de l\'estimation
                                </h2>

                                <!-- Composant Vue de test -->
                                <test-component></test-component>

                                <!-- Composant d\'estimation -->
                                <div class="mt-8">
                                    <estimation-form></estimation-form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- Vue.js via CDN -->
        <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
        <!-- Flowbite -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.0/flowbite.min.js"></script>

        <!-- Application Vue.js -->
        <script>
        const { createApp } = Vue;

        // Composant de test
        const TestComponent = {
            template: `
                <div class="p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                        🎉 Vue.js est connecté !
                    </h3>
                    <p class="text-blue-700 dark:text-blue-300 mb-4">
                        Compteur: {{ count }}
                    </p>
                    <button
                        @click="increment"
                        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200"
                    >
                        Incrémenter
                    </button>
                </div>
            `,
            data() {
                return {
                    count: 0
                }
            },
            methods: {
                increment() {
                    this.count++;
                }
            }
        };

        // Composant d\'estimation
        const EstimationForm = {
            template: `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                        Informations de base du projet
                    </h3>

                    <form @submit.prevent="submitEstimation" class="space-y-6">
                        <!-- Type de projet -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Type de projet
                            </label>
                            <select v-model="formData.type_projet"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                <option value="site-vitrine">Site vitrine</option>
                                <option value="saas">SaaS</option>
                                <option value="e-commerce">E-commerce</option>
                                <option value="api">API</option>
                                <option value="app-mobile">App mobile</option>
                                <option value="dashboard">Dashboard</option>
                                <option value="autre">Autre</option>
                            </select>
                        </div>

                        <!-- Technologies -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Technologies principales
                            </label>
                            <input v-model="formData.technologies"
                                   type="text"
                                   placeholder="Ex: PHP, Vue.js, MySQL"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        </div>

                        <!-- Nombre de pages -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Nombre de pages/écrans
                            </label>
                            <input v-model.number="formData.nb_pages"
                                   type="number"
                                   min="1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        </div>

                        <!-- Bouton de soumission -->
                        <div>
                            <button type="submit"
                                    :disabled="loading"
                                    class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200">
                                {{ loading ? \'Calcul en cours...\' : \'Obtenir une estimation\' }}
                            </button>
                        </div>
                    </form>

                    <!-- Résultats -->
                    <div v-if="estimation" class="mt-8 p-6 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                        <h4 class="text-lg font-semibold text-green-900 dark:text-green-100 mb-4">
                            📊 Estimation du projet
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-green-700 dark:text-green-300">Durée estimée</p>
                                <p class="text-xl font-bold text-green-900 dark:text-green-100">{{ estimation.duree_jours }} jours</p>
                            </div>
                            <div>
                                <p class="text-sm text-green-700 dark:text-green-300">Coût total</p>
                                <p class="text-xl font-bold text-green-900 dark:text-green-100">{{ estimation.cout_total.toLocaleString() }} €</p>
                            </div>
                            <div>
                                <p class="text-sm text-green-700 dark:text-green-300">TJM recommandé</p>
                                <p class="text-xl font-bold text-green-900 dark:text-green-100">{{ estimation.tjm_recommande }} €</p>
                            </div>
                            <div>
                                <p class="text-sm text-green-700 dark:text-green-300">Complexité</p>
                                <p class="text-xl font-bold text-green-900 dark:text-green-100">{{ estimation.complexite }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            data() {
                return {
                    loading: false,
                    estimation: null,
                    formData: {
                        type_projet: \'site-vitrine\',
                        technologies: \'PHP, JavaScript\',
                        nb_pages: 5
                    }
                }
            },
            methods: {
                async submitEstimation() {
                    this.loading = true;
                    try {
                        const response = await fetch(\'/api/estimation\', {
                            method: \'POST\',
                            headers: {
                                \'Content-Type\': \'application/json\'
                            },
                            body: JSON.stringify(this.formData)
                        });

                        const data = await response.json();
                        if (data.success) {
                            this.estimation = data.estimation;
                        }
                    } catch (error) {
                        console.error(\'Erreur lors de l\\\'estimation:\', error);
                    } finally {
                        this.loading = false;
                    }
                }
            }
        };

        // Application Vue.js
        const app = createApp({
            components: {
                \'test-component\': TestComponent,
                \'estimation-form\': EstimationForm
            }
        });

        app.mount(\'#estimation-app\');
        </script>

        <script defer src="/build/app.cabe8d39.js"></script>
    </body>
</html>';
        break;

    case '/api/estimation':
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // API mock simple
            header('Content-Type: application/json');
            $data = json_decode(file_get_contents('php://input'), true);

            $mockEstimation = [
                'success' => true,
                'estimation' => [
                    'duree_jours' => rand(15, 60),
                    'cout_total' => rand(5000, 25000),
                    'tjm_recommande' => rand(400, 800),
                    'complexite' => ['Faible', 'Modérée', 'Élevée', 'Très élevée'][rand(0, 3)],
                    'marge_securite' => '20%',
                    'details' => [
                        'type_projet' => $data['type_projet'] ?? 'Site vitrine',
                        'technologies' => $data['technologies'] ?? ['PHP', 'JavaScript'],
                        'nb_pages' => $data['nb_pages'] ?? 5,
                        'fonctionnalites' => $data['fonctionnalites'] ?? []
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ];

            echo json_encode($mockEstimation);
        } else {
            http_response_code(405);
            echo 'Method Not Allowed';
        }
        break;

    default:
        http_response_code(404);
        echo '404 - Page not found';
        break;
}
