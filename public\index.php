<?php

// Routing simple basé sur l'URL
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Routing simple
switch ($path) {
    case '/':
        echo '<h1>QuickEsti</h1><p><a href="/estimation">Aller à l\'estimation</a></p>';
        break;

    case '/estimation':
        // Rendu direct de la page d'estimation
        echo '<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <title>QuickEsti - Estimation de projet</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="/build/app.94f78fc5.css" rel="stylesheet">
    </head>
    <body class="bg-gray-50 dark:bg-gray-900">
        <div class="bg-gray-100 dark:bg-gray-900 min-h-screen">
            <!-- Header -->
            <header class="bg-white dark:bg-gray-800 shadow">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                        QuickEsti 💼📊
                    </h1>
                    <p class="mt-2 text-lg text-gray-600 dark:text-gray-400">
                        Estimez le coût et la durée de votre projet web en quelques clics
                    </p>
                </div>
            </header>

            <!-- Main Content -->
            <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div class="px-4 py-6 sm:px-0">
                    <!-- Vue.js App Container -->
                    <div id="estimation-app">
                        <!-- Le contenu Vue.js sera injecté ici -->
                        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                            <div class="px-4 py-5 sm:p-6">
                                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                                    Configuration de l\'estimation
                                </h2>

                                <!-- Composant Vue de test -->
                                <test-component></test-component>

                                <!-- Composant d\'estimation -->
                                <div class="mt-8">
                                    <estimation-form></estimation-form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- Vue.js via CDN -->
        <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
        <!-- Flowbite -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.0/flowbite.min.js"></script>

        <!-- Application Vue.js -->
        <script>
        const { createApp } = Vue;

        // Composant de test
        const TestComponent = {
            template: `
                <div class="p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                        🎉 Vue.js est connecté !
                    </h3>
                    <p class="text-blue-700 dark:text-blue-300 mb-4">
                        Compteur: {{ count }}
                    </p>
                    <button
                        @click="increment"
                        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200"
                    >
                        Incrémenter
                    </button>
                </div>
            `,
            data() {
                return {
                    count: 0
                }
            },
            methods: {
                increment() {
                    this.count++;
                }
            }
        };






        // Composant principal EstimationForm - Version complète
        const EstimationForm = {
            template: `
                <div>
                    <!-- Sélecteur de type d\'utilisateur -->
                    <user-type-selector v-model="userType"></user-type-selector>

                    <!-- Formulaires conditionnels -->
                    <div v-if="userType">
                        <!-- Formulaire Freelance -->
                        <div v-if="userType === \'freelance\'">
                            <div class="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                <h2 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                                    👨‍💻 Formulaire Développeur Freelance
                                </h2>
                                <p class="text-sm text-blue-700 dark:text-blue-300">
                                    Estimez votre projet en tant que freelance
                                </p>
                            </div>

                            <freelance-basics></freelance-basics>
                            <freelance-constraints></freelance-constraints>
                            <freelance-features></freelance-features>
                            <freelance-deliverables></freelance-deliverables>
                            <freelance-objectives></freelance-objectives>
                        </div>

                        <!-- Formulaire Entreprise -->
                        <div v-if="userType === \'business\'">
                            <div class="mb-6 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                                <h2 class="text-lg font-semibold text-purple-900 dark:text-purple-100 mb-2">
                                    🏢 Formulaire Entreprise
                                </h2>
                                <p class="text-sm text-purple-700 dark:text-purple-300">
                                    Estimez votre projet en tant qu\'entreprise ou client
                                </p>
                            </div>

                            <business-basics></business-basics>
                            <business-structure></business-structure>
                            <business-features></business-features>
                            <business-deliverables></business-deliverables>
                            <business-objectives></business-objectives>
                            <business-pricing></business-pricing>
                        </div>

                        <!-- Bouton de soumission -->
                        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                            <button
                                @click="submitEstimation"
                                :disabled="loading"
                                class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-500 text-white font-medium py-3 px-6 rounded-lg transition-all duration-200 transform hover:scale-105"
                            >
                                {{ loading ? \'Calcul en cours...\' : \'🚀 Obtenir mon estimation\' }}
                            </button>
                        </div>

                        <!-- Résultats -->
                        <div v-if="estimation" class="mt-8 p-6 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                            <h4 class="text-lg font-semibold text-green-900 dark:text-green-100 mb-4">
                                📊 Estimation du projet
                            </h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p class="text-sm text-green-700 dark:text-green-300">Durée estimée</p>
                                    <p class="text-xl font-bold text-green-900 dark:text-green-100">{{ estimation.duree_jours }} jours</p>
                                </div>
                                <div>
                                    <p class="text-sm text-green-700 dark:text-green-300">Coût total</p>
                                    <p class="text-xl font-bold text-green-900 dark:text-green-100">{{ estimation.cout_total.toLocaleString() }} €</p>
                                </div>
                                <div>
                                    <p class="text-sm text-green-700 dark:text-green-300">TJM recommandé</p>
                                    <p class="text-xl font-bold text-green-900 dark:text-green-100">{{ estimation.tjm_recommande }} €</p>
                                </div>
                                <div>
                                    <p class="text-sm text-green-700 dark:text-green-300">Complexité</p>
                                    <p class="text-xl font-bold text-green-900 dark:text-green-100">{{ estimation.complexite }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Message d\'aide si aucun type sélectionné -->
                    <div v-else class="text-center py-8">
                        <p class="text-gray-500 dark:text-gray-400">
                            👆 Sélectionnez votre profil pour commencer l\'estimation
                        </p>
                    </div>
                </div>
            `,
            data() {
                return {
                    userType: null, // \'freelance\' ou \'business\'
                    loading: false,
                    estimation: null
                }
            },
            methods: {
                async submitEstimation() {
                    this.loading = true;
                    try {
                        const formData = {
                            user_type: this.userType,
                            type_projet: \'estimation-test\',
                            technologies: \'Vue.js, Symfony\',
                            nb_pages: 5
                        };

                        const response = await fetch(\'/api/estimation\', {
                            method: \'POST\',
                            headers: {
                                \'Content-Type\': \'application/json\'
                            },
                            body: JSON.stringify(formData)
                        });

                        const data = await response.json();
                        if (data.success) {
                            this.estimation = data.estimation;
                        }
                    } catch (error) {
                        console.error(\'Erreur lors de l\\\'estimation:\', error);
                    } finally {
                        this.loading = false;
                    }
                }
            }
        };

        // Composants pour les freelances
        const FreelanceBasics = {
            template: `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        📋 Section 1 : Informations de base
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">
                        Type de projet, technologies, deadline, nombre de pages...
                    </p>

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Type de projet
                            </label>
                            <select class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                <option>Site vitrine</option>
                                <option>SaaS</option>
                                <option>E-commerce</option>
                                <option>API</option>
                                <option>App mobile</option>
                                <option>Dashboard</option>
                                <option>Autre</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Technologies à utiliser
                            </label>
                            <input type="text" placeholder="Ex: PHP, Vue.js, MySQL"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Deadline (nombre de jours)
                            </label>
                            <input type="number" placeholder="Ex: 30"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Nombre de pages
                            </label>
                            <input type="number" placeholder="Ex: 5"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                        </div>
                    </div>

                    <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                        <p class="text-sm text-blue-700 dark:text-blue-300">
                            🚧 Composant FreelanceBasics - Structure de base créée, à compléter
                        </p>
                    </div>
                </div>
            `
        };

        const FreelanceConstraints = {
            template: `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        ⚙️ Section 2 : Contraintes du freelance
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        Niveau de compétence, temps plein, TJM cible, marge de sécurité...
                    </p>
                    <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                        <p class="text-sm text-blue-700 dark:text-blue-300">
                            🚧 Composant FreelanceConstraints - À développer
                        </p>
                    </div>
                </div>
            `
        };

        const FreelanceFeatures = {
            template: `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        🔧 Section 3 : Fonctionnalités additionnelles
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        Authentification, tableau de bord, API externes, paiement...
                    </p>
                    <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                        <p class="text-sm text-blue-700 dark:text-blue-300">
                            🚧 Composant FreelanceFeatures - À développer
                        </p>
                    </div>
                </div>
            `
        };

        const FreelanceDeliverables = {
            template: `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        📦 Section 4 : Livrable & périmètre attendu
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        Dev seul ou + UI/UX, maquettes, réunions, hébergement...
                    </p>
                    <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                        <p class="text-sm text-blue-700 dark:text-blue-300">
                            🚧 Composant FreelanceDeliverables - À développer
                        </p>
                    </div>
                </div>
            `
        };

        const FreelanceObjectives = {
            template: `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        🎯 Section 5 : Objectif personnel
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        Rentabilité, portfolio, progression, client stratégique...
                    </p>
                    <div class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                        <p class="text-sm text-blue-700 dark:text-blue-300">
                            🚧 Composant FreelanceObjectives - À développer
                        </p>
                    </div>
                </div>
            `
        };

        // Composants pour les entreprises
        const BusinessBasics = {
            template: `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        📋 Section 1 : Informations de base
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        Type de projet, technologies, deadline, pourquoi chiffrer ce projet...
                    </p>
                    <div class="mt-4 p-4 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-200 dark:border-purple-800">
                        <p class="text-sm text-purple-700 dark:text-purple-300">
                            🚧 Composant BusinessBasics - À développer
                        </p>
                    </div>
                </div>
            `
        };

        const BusinessStructure = {
            template: `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        🏗️ Section 2 : Structure & organisation de l\'entreprise
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        Rôle, équipe projet, méthodologie, nombre de personnes...
                    </p>
                    <div class="mt-4 p-4 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-200 dark:border-purple-800">
                        <p class="text-sm text-purple-700 dark:text-purple-300">
                            🚧 Composant BusinessStructure - À développer
                        </p>
                    </div>
                </div>
            `
        };

        const BusinessFeatures = {
            template: `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        🔧 Section 3 : Fonctionnalités et périmètre fonctionnel
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        SSO, API, e-commerce, scalabilité, complexité fonctionnelle...
                    </p>
                    <div class="mt-4 p-4 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-200 dark:border-purple-800">
                        <p class="text-sm text-purple-700 dark:text-purple-300">
                            🚧 Composant BusinessFeatures - À développer
                        </p>
                    </div>
                </div>
            `
        };

        const BusinessDeliverables = {
            template: `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        📦 Section 4 : Livrable attendu & périmètre
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        UI/UX, maquettes, spécifications, déploiement, monitoring...
                    </p>
                    <div class="mt-4 p-4 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-200 dark:border-purple-800">
                        <p class="text-sm text-purple-700 dark:text-purple-300">
                            🚧 Composant BusinessDeliverables - À développer
                        </p>
                    </div>
                </div>
            `
        };

        const BusinessObjectives = {
            template: `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        🎯 Section 5 : Objectifs business du projet
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        MVP, production, budget, urgence du projet...
                    </p>
                    <div class="mt-4 p-4 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-200 dark:border-purple-800">
                        <p class="text-sm text-purple-700 dark:text-purple-300">
                            🚧 Composant BusinessObjectives - À développer
                        </p>
                    </div>
                </div>
            `
        };

        const BusinessPricing = {
            template: `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        💰 Section 6 : Structure tarifaire
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400">
                        Coût journalier par profil, marge, forfait vs régie...
                    </p>
                    <div class="mt-4 p-4 bg-purple-50 dark:bg-purple-900/20 rounded border border-purple-200 dark:border-purple-800">
                        <p class="text-sm text-purple-700 dark:text-purple-300">
                            🚧 Composant BusinessPricing - À développer
                        </p>
                    </div>
                </div>
            `
        };

        // Application Vue.js
        const app = createApp({
            components: {
                \'test-component\': TestComponent,
                \'estimation-form\': EstimationForm,
                \'user-type-selector\': UserTypeSelector,
                // Composants Freelance
                \'freelance-basics\': FreelanceBasics,
                \'freelance-constraints\': FreelanceConstraints,
                \'freelance-features\': FreelanceFeatures,
                \'freelance-deliverables\': FreelanceDeliverables,
                \'freelance-objectives\': FreelanceObjectives,
                // Composants Business
                \'business-basics\': BusinessBasics,
                \'business-structure\': BusinessStructure,
                \'business-features\': BusinessFeatures,
                \'business-deliverables\': BusinessDeliverables,
                \'business-objectives\': BusinessObjectives,
                \'business-pricing\': BusinessPricing
            }
        });

        app.mount(\'#estimation-app\');
        </script>

        <script defer src="/build/app.cabe8d39.js"></script>
    </body>
</html>';
        break;

    case '/api/estimation':
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // API mock simple
            header('Content-Type: application/json');
            $data = json_decode(file_get_contents('php://input'), true);

            $mockEstimation = [
                'success' => true,
                'estimation' => [
                    'duree_jours' => rand(15, 60),
                    'cout_total' => rand(5000, 25000),
                    'tjm_recommande' => rand(400, 800),
                    'complexite' => ['Faible', 'Modérée', 'Élevée', 'Très élevée'][rand(0, 3)],
                    'marge_securite' => '20%',
                    'details' => [
                        'type_projet' => $data['type_projet'] ?? 'Site vitrine',
                        'technologies' => $data['technologies'] ?? ['PHP', 'JavaScript'],
                        'nb_pages' => $data['nb_pages'] ?? 5,
                        'fonctionnalites' => $data['fonctionnalites'] ?? []
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ];

            echo json_encode($mockEstimation);
        } else {
            http_response_code(405);
            echo 'Method Not Allowed';
        }
        break;

    default:
        http_response_code(404);
        echo '404 - Page not found';
        break;
}
