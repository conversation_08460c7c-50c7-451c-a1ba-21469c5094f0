<?php

// Routing simple basé sur l'URL
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Routing simple
switch ($path) {
    case '/':
        echo '<h1>QuickEsti</h1><p><a href="/estimation">Aller à l\'estimation</a></p>';
        break;

    case '/estimation':
        // Rendu direct de la page d'estimation
        echo '<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <title>QuickEsti - Estimation de projet</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="/build/app.94f78fc5.css" rel="stylesheet">
    </head>
    <body class="bg-gray-50 dark:bg-gray-900">
        <div class="bg-gray-100 dark:bg-gray-900 min-h-screen">
            <!-- Header -->
            <header class="bg-white dark:bg-gray-800 shadow">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                        QuickEsti 💼📊
                    </h1>
                    <p class="mt-2 text-lg text-gray-600 dark:text-gray-400">
                        Estimez le coût et la durée de votre projet web en quelques clics
                    </p>
                </div>
            </header>

            <!-- Main Content -->
            <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div class="px-4 py-6 sm:px-0">
                    <!-- Vue.js App Container -->
                    <div id="estimation-app">
                        <!-- Le contenu Vue.js sera injecté ici -->
                        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                            <div class="px-4 py-5 sm:p-6">
                                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                                    Configuration de l\'estimation
                                </h2>

                                <!-- Composant Vue de test -->
                                <test-component></test-component>

                                <!-- Composant d\'estimation -->
                                <div class="mt-8">
                                    <estimation-form></estimation-form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- Vue.js via CDN -->
        <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
        <!-- Flowbite -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.0/flowbite.min.js"></script>

        <!-- Application Vue.js -->
        <script>
        const { createApp } = Vue;

        // Composant de test
        const TestComponent = {
            template: `
                <div class="p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                        🎉 Vue.js est connecté !
                    </h3>
                    <p class="text-blue-700 dark:text-blue-300 mb-4">
                        Compteur: {{ count }}
                    </p>
                    <button
                        @click="increment"
                        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200"
                    >
                        Incrémenter
                    </button>
                </div>
            `,
            data() {
                return {
                    count: 0
                }
            },
            methods: {
                increment() {
                    this.count++;
                }
            }
        };



        // Version de test ultra simple
        const EstimationForm = {
            template: `
                <div>
                    <h2>TEST: EstimationForm chargé</h2>

                    <!-- Sélecteur simple intégré -->
                    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                        <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                            Qui êtes-vous ?
                        </h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <button
                                @click="selectType(\'freelance\')"
                                :class="[
                                    \'p-6 border-2 rounded-lg transition-all duration-200 text-left\',
                                    userType === \'freelance\'
                                        ? \'border-blue-500 bg-blue-50 dark:bg-blue-900/20\'
                                        : \'border-gray-200 dark:border-gray-600 hover:border-blue-300\'
                                ]"
                            >
                                <div class="flex items-center mb-2">
                                    <span class="text-2xl mr-3">👨‍💻</span>
                                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white">
                                        Développeur freelance
                                    </h4>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    J\'estime un projet pour un client
                                </p>
                            </button>

                            <button
                                @click="selectType(\'business\')"
                                :class="[
                                    \'p-6 border-2 rounded-lg transition-all duration-200 text-left\',
                                    userType === \'business\'
                                        ? \'border-blue-500 bg-blue-50 dark:bg-blue-900/20\'
                                        : \'border-gray-200 dark:border-gray-600 hover:border-blue-300\'
                                ]"
                            >
                                <div class="flex items-center mb-2">
                                    <span class="text-2xl mr-3">🏢</span>
                                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white">
                                        Entreprise ou client
                                    </h4>
                                </div>
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    J\'estime un projet pour mon entreprise
                                </p>
                            </button>
                        </div>
                    </div>

                    <!-- Affichage conditionnel -->
                    <div v-if="userType === \'freelance\'" class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 mb-6">
                        <h2 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                            👨‍💻 Formulaire Développeur Freelance
                        </h2>
                        <p class="text-blue-700 dark:text-blue-300">
                            Sections freelance à développer...
                        </p>
                    </div>

                    <div v-if="userType === \'business\'" class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-6 mb-6">
                        <h2 class="text-lg font-semibold text-purple-900 dark:text-purple-100 mb-2">
                            🏢 Formulaire Entreprise
                        </h2>
                        <p class="text-purple-700 dark:text-purple-300">
                            Sections entreprise à développer...
                        </p>
                    </div>

                    <div v-if="!userType" class="text-center py-8">
                        <p class="text-gray-500 dark:text-gray-400">
                            👆 Sélectionnez votre profil pour commencer l\'estimation
                        </p>
                    </div>
                </div>
            `,
            data() {
                return {
                    userType: null
                }
            },
            methods: {
                selectType(type) {
                    this.userType = type;
                    console.log(\'Type sélectionné:\', type);
                }
            }
        };



        // Application Vue.js - Version simplifiée
        const app = createApp({
            components: {
                \'test-component\': TestComponent,
                \'estimation-form\': EstimationForm
            }
        });

        app.mount(\'#estimation-app\');
        </script>

        <script defer src="/build/app.cabe8d39.js"></script>
    </body>
</html>';
        break;

    case '/api/estimation':
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // API mock simple
            header('Content-Type: application/json');
            $data = json_decode(file_get_contents('php://input'), true);

            $mockEstimation = [
                'success' => true,
                'estimation' => [
                    'duree_jours' => rand(15, 60),
                    'cout_total' => rand(5000, 25000),
                    'tjm_recommande' => rand(400, 800),
                    'complexite' => ['Faible', 'Modérée', 'Élevée', 'Très élevée'][rand(0, 3)],
                    'marge_securite' => '20%',
                    'details' => [
                        'type_projet' => $data['type_projet'] ?? 'Site vitrine',
                        'technologies' => $data['technologies'] ?? ['PHP', 'JavaScript'],
                        'nb_pages' => $data['nb_pages'] ?? 5,
                        'fonctionnalites' => $data['fonctionnalites'] ?? []
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ];

            echo json_encode($mockEstimation);
        } else {
            http_response_code(405);
            echo 'Method Not Allowed';
        }
        break;

    default:
        http_response_code(404);
        echo '404 - Page not found';
        break;
}
