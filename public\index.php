<?php

// Routing simple basé sur l'URL
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);

// Routing simple
switch ($path) {
    case '/':
        echo '<h1>QuickEsti</h1><p><a href="/estimation">Aller à l\'estimation</a></p>';
        break;

    case '/estimation':
        // Rendu direct de la page d'estimation
        echo '<!DOCTYPE html>
<html>
    <head>
        <meta charset="UTF-8">
        <title>QuickEsti - Estimation de projet</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link href="/build/app.94f78fc5.css" rel="stylesheet">
    </head>
    <body class="bg-gray-50 dark:bg-gray-900">
        <div class="bg-gray-100 dark:bg-gray-900 min-h-screen">
            <!-- Header -->
            <header class="bg-white dark:bg-gray-800 shadow">
                <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                        QuickEsti 💼📊
                    </h1>
                    <p class="mt-2 text-lg text-gray-600 dark:text-gray-400">
                        Estimez le coût et la durée de votre projet web en quelques clics
                    </p>
                </div>
            </header>

            <!-- Main Content -->
            <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                <div class="px-4 py-6 sm:px-0">
                    <!-- Vue.js App Container -->
                    <div id="estimation-app">
                        <!-- Le contenu Vue.js sera injecté ici -->
                        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                            <div class="px-4 py-5 sm:p-6">
                                <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                                    Configuration de l\'estimation
                                </h2>

                                <!-- Composant Vue de test -->
                                <test-component></test-component>

                                <!-- Composant d\'estimation -->
                                <div class="mt-8">
                                    <estimation-form></estimation-form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- Vue.js via CDN -->
        <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
        <!-- Flowbite -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.0/flowbite.min.js"></script>

        <!-- Application Vue.js -->
        <script>
        const { createApp } = Vue;

        // Composant de test
        const TestComponent = {
            template: `
                <div class="p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                    <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                        🎉 Vue.js est connecté !
                    </h3>
                    <p class="text-blue-700 dark:text-blue-300 mb-4">
                        Compteur: {{ count }}
                    </p>
                    <button
                        @click="increment"
                        class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200"
                    >
                        Incrémenter
                    </button>
                </div>
            `,
            data() {
                return {
                    count: 0
                }
            },
            methods: {
                increment() {
                    this.count++;
                }
            }
        };



        // Composant UserTypeSelector
        const UserTypeSelector = {
            template: `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                        Qui êtes-vous ?
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <button
                            @click="selectType(\'freelance\')"
                            :class="[
                                \'p-6 border-2 rounded-lg transition-all duration-200 text-left\',
                                selectedType === \'freelance\'
                                    ? \'border-blue-500 bg-blue-50 dark:bg-blue-900/20\'
                                    : \'border-gray-200 dark:border-gray-600 hover:border-blue-300\'
                            ]"
                        >
                            <div class="flex items-center mb-2">
                                <span class="text-2xl mr-3">👨‍💻</span>
                                <h4 class="text-lg font-semibold text-gray-900 dark:text-white">
                                    Développeur freelance
                                </h4>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                J\'estime un projet pour un client
                            </p>
                        </button>

                        <button
                            @click="selectType(\'business\')"
                            :class="[
                                \'p-6 border-2 rounded-lg transition-all duration-200 text-left\',
                                selectedType === \'business\'
                                    ? \'border-blue-500 bg-blue-50 dark:bg-blue-900/20\'
                                    : \'border-gray-200 dark:border-gray-600 hover:border-blue-300\'
                            ]"
                        >
                            <div class="flex items-center mb-2">
                                <span class="text-2xl mr-3">🏢</span>
                                <h4 class="text-lg font-semibold text-gray-900 dark:text-white">
                                    Entreprise ou client
                                </h4>
                            </div>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                J\'estime un projet pour mon entreprise
                            </p>
                        </button>
                    </div>
                </div>
            `,
            props: [\'selectedType\'],
            emits: [\'selected\'],
            methods: {
                selectType(type) {
                    this.$emit(\'selected\', type);
                }
            }
        };

        // Composant FreelanceBasics - Section 1: Info de base
        const FreelanceBasics = {
            template: `
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                        📋 Section 1 : Informations de base
                    </h3>

                    <div class="space-y-6">
                        <!-- Type de projet -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Type de projet *
                            </label>
                            <select
                                v-model="formData.typeProjet"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            >
                                <option value="">Sélectionnez un type</option>
                                <option value="site-vitrine">Site vitrine</option>
                                <option value="saas">SaaS</option>
                                <option value="e-commerce">E-commerce</option>
                                <option value="api">API</option>
                                <option value="app-mobile">App mobile</option>
                                <option value="dashboard">Dashboard</option>
                                <option value="autre">Autre</option>
                            </select>
                            <input
                                v-if="formData.typeProjet === \'autre\'"
                                v-model="formData.typeProjetAutre"
                                type="text"
                                placeholder="Précisez le type de projet"
                                class="mt-2 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            >
                        </div>

                        <!-- Technologies -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Technologies à utiliser *
                            </label>
                            <textarea
                                v-model="formData.technologies"
                                placeholder="Ex: PHP, Vue.js, MySQL, Tailwind CSS..."
                                rows="3"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            ></textarea>
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                Listez les technologies imposées par le projet ou que vous comptez utiliser
                            </p>
                        </div>

                        <!-- Projet existant -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                                Le projet est-il déjà existant ?
                            </label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input
                                        type="radio"
                                        v-model="formData.projetExistant"
                                        value="nouveau"
                                        class="mr-3 text-blue-600"
                                    >
                                    <span class="text-gray-700 dark:text-gray-300">Nouveau projet (from scratch)</span>
                                </label>
                                <label class="flex items-center">
                                    <input
                                        type="radio"
                                        v-model="formData.projetExistant"
                                        value="existant"
                                        class="mr-3 text-blue-600"
                                    >
                                    <span class="text-gray-700 dark:text-gray-300">Projet existant à améliorer/modifier</span>
                                </label>
                            </div>
                        </div>

                        <!-- Deadline -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Deadline (nombre de jours pour réaliser le projet)
                            </label>
                            <input
                                v-model.number="formData.deadline"
                                type="number"
                                min="1"
                                placeholder="Ex: 30"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            >
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                Laissez vide si pas de contrainte de temps
                            </p>
                        </div>

                        <!-- Nombre de pages -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Nombre de pages/écrans *
                            </label>
                            <input
                                v-model.number="formData.nombrePages"
                                type="number"
                                min="1"
                                placeholder="Ex: 5"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                            >
                        </div>
                    </div>

                    <!-- Debug info -->
                    <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded border border-blue-200 dark:border-blue-800">
                        <p class="text-sm text-blue-700 dark:text-blue-300 mb-2">
                            🚧 Composant FreelanceBasics - Section développée
                        </p>
                        <pre class="text-xs text-blue-600 dark:text-blue-400">{{ JSON.stringify(formData, null, 2) }}</pre>
                    </div>
                </div>
            `,
            data() {
                return {
                    formData: {
                        typeProjet: \'\',
                        typeProjetAutre: \'\',
                        technologies: \'\',
                        projetExistant: \'\',
                        deadline: null,
                        nombrePages: null
                    }
                }
            }
        };

        // Composant principal EstimationForm avec la nouvelle structure
        const EstimationForm = {
            template: `
                <div>
                    <user-type-selector
                        :selectedType="userType"
                        @selected="setUserType"
                    ></user-type-selector>

                    <template v-if="userType === \'freelance\'">
                        <div class="mb-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                            <h2 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                                👨‍💻 Formulaire Développeur Freelance
                            </h2>
                            <p class="text-sm text-blue-700 dark:text-blue-300">
                                Remplissez les sections ci-dessous pour obtenir votre estimation
                            </p>
                        </div>
                        <freelance-basics></freelance-basics>
                    </template>

                    <template v-else-if="userType === \'business\'">
                        <div class="mb-6 p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                            <h2 class="text-lg font-semibold text-purple-900 dark:text-purple-100 mb-2">
                                🏢 Formulaire Entreprise
                            </h2>
                            <p class="text-sm text-purple-700 dark:text-purple-300">
                                Sections entreprise à développer...
                            </p>
                        </div>
                    </template>

                    <div v-if="!userType" class="text-center py-8">
                        <p class="text-gray-500 dark:text-gray-400">
                            👆 Sélectionnez votre profil pour commencer l\'estimation
                        </p>
                    </div>
                </div>
            `,
            data() {
                return {
                    userType: null
                }
            },
            methods: {
                setUserType(type) {
                    this.userType = type;
                    console.log(\'Type utilisateur sélectionné:\', type);
                }
            }
        };



        // Application Vue.js - Version avec composants propres
        const app = createApp({
            components: {
                \'test-component\': TestComponent,
                \'estimation-form\': EstimationForm,
                \'user-type-selector\': UserTypeSelector,
                \'freelance-basics\': FreelanceBasics
            }
        });

        app.mount(\'#estimation-app\');
        </script>

        <script defer src="/build/app.cabe8d39.js"></script>
    </body>
</html>';
        break;

    case '/api/estimation':
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // API mock simple
            header('Content-Type: application/json');
            $data = json_decode(file_get_contents('php://input'), true);

            $mockEstimation = [
                'success' => true,
                'estimation' => [
                    'duree_jours' => rand(15, 60),
                    'cout_total' => rand(5000, 25000),
                    'tjm_recommande' => rand(400, 800),
                    'complexite' => ['Faible', 'Modérée', 'Élevée', 'Très élevée'][rand(0, 3)],
                    'marge_securite' => '20%',
                    'details' => [
                        'type_projet' => $data['type_projet'] ?? 'Site vitrine',
                        'technologies' => $data['technologies'] ?? ['PHP', 'JavaScript'],
                        'nb_pages' => $data['nb_pages'] ?? 5,
                        'fonctionnalites' => $data['fonctionnalites'] ?? []
                    ]
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ];

            echo json_encode($mockEstimation);
        } else {
            http_response_code(405);
            echo 'Method Not Allowed';
        }
        break;

    default:
        http_response_code(404);
        echo '404 - Page not found';
        break;
}
