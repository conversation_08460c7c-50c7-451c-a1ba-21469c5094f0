{% extends 'base.html.twig' %}

{% block title %}QuickEsti - Estimation de projet{% endblock %}

{% block body %}
<div class="bg-gray-100 dark:bg-gray-900 min-h-screen">
    <!-- Header -->
    <header class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                QuickEsti 💼📊
            </h1>
            <p class="mt-2 text-lg text-gray-600 dark:text-gray-400">
                Estimez le coût et la durée de votre projet web en quelques clics
            </p>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div class="px-4 py-6 sm:px-0">
            <!-- Vue.js App Container -->
            <div id="estimation-app">
                <!-- Le contenu Vue.js sera injecté ici -->
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                    <div class="px-4 py-5 sm:p-6">
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                            Configuration de l'estimation
                        </h2>
                        
                        <!-- Composant Vue de test -->
                        <test-component></test-component>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Vue.js via CDN -->
<script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
<!-- Flowbite -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.0/flowbite.min.js"></script>

<!-- Application Vue.js -->
<script>
const { createApp } = Vue;

// Composant de test
const TestComponent = {
    template: `
        <div class="p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
                🎉 Vue.js est connecté !
            </h3>
            <p class="text-blue-700 dark:text-blue-300 mb-4">
                Compteur: {{ count }}
            </p>
            <button 
                @click="increment" 
                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200"
            >
                Incrémenter
            </button>
        </div>
    `,
    data() {
        return {
            count: 0
        }
    },
    methods: {
        increment() {
            this.count++;
        }
    }
};

// Application Vue.js
const app = createApp({
    components: {
        'test-component': TestComponent
    }
});

app.mount('#estimation-app');
</script>
{% endblock %}
