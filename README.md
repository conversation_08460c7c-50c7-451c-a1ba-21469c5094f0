# QuickEsti 💼📊

Application d'estimation de projets web développée avec Symfony + Vue.js + Tailwind CSS + Flowbite.

## Architecture

- **Backend**: Symfony (PHP)
- **Frontend**: Vue.js 3 via CDN intégré dans Twig
- **CSS**: Tailwind CSS + Flowbite
- **Build**: Webpack Encore
- **Structure**: Composants Vue.js dans `assets/js/components`

## Installation

1. C<PERSON>r le projet
2. Installer les dépendances Node.js :
   ```bash
   npm install
   ```

3. Compiler les assets :
   ```bash
   npm run build
   ```

## Développement

### Démarrer le serveur de développement

```bash
# Compiler les assets en mode développement
npm run dev

# Ou en mode watch (recompilation automatique)
npm run watch

# Démarrer le serveur PHP
npm run serve
# ou
php -S localhost:8000 -t public
```

### Accéder à l'application

- Page d'estimation : http://localhost:8000/estimation
- API d'estimation : http://localhost:8000/api/estimation (POST)

## Fonctionnalités actuelles

### ✅ Implémenté

- [x] Setup complet Symfony + Vue.js + Tailwind + Flowbite
- [x] Page d'estimation avec composant Vue.js fonctionnel
- [x] API mock d'estimation
- [x] **Sélecteur de type d'utilisateur** (Freelance vs Entreprise)
- [x] **Structure conditionnelle du formulaire** selon le profil
- [x] **Composants placeholders** pour toutes les sections :
  - **Freelance** : Basics, Constraints, Features, Deliverables, Objectives
  - **Entreprise** : Basics, Structure, Features, Deliverables, Objectives, Pricing
- [x] Interface responsive avec Tailwind CSS
- [x] Affichage des résultats d'estimation

### 🚧 À développer

- [ ] **Développement complet des composants** :
  - [ ] FreelanceBasics (type projet, technologies, deadline, pages)
  - [ ] FreelanceConstraints (compétences, temps plein, TJM, marge)
  - [ ] FreelanceFeatures (fonctionnalités additionnelles)
  - [ ] FreelanceDeliverables (périmètre, maquettes, hébergement)
  - [ ] FreelanceObjectives (objectifs personnels)
  - [ ] BusinessBasics (infos de base entreprise)
  - [ ] BusinessStructure (organisation, équipe, méthodologie)
  - [ ] BusinessFeatures (fonctionnalités entreprise)
  - [ ] BusinessDeliverables (livrables attendus)
  - [ ] BusinessObjectives (objectifs business)
  - [ ] BusinessPricing (structure tarifaire)
- [ ] **Logique métier d'estimation avancée** selon le profil
- [ ] **Gestion des données** entre composants (state management)
- [ ] **Validation des formulaires**
- [ ] **Fonctionnalités premium**
- [ ] **Export PDF**
- [ ] **Authentification**
- [ ] **Base de données**

## Structure du projet

```
QuickEsti/
├── assets/
│   ├── js/
│   │   ├── components/          # Composants Vue.js
│   │   └── estimation.js        # Point d'entrée estimation
│   └── styles/
│       └── app.css              # Styles Tailwind
├── config/                      # Configuration Symfony
├── public/
│   ├── build/                   # Assets compilés
│   └── index.php                # Point d'entrée
├── src/
│   └── Controller/              # Controllers Symfony
├── templates/                   # Templates Twig
├── package.json                 # Dépendances Node.js
├── webpack.config.js            # Configuration Webpack Encore
└── tailwind.config.js           # Configuration Tailwind
```

## Prochaines étapes

1. Développer les composants Vue.js pour chaque section du formulaire
2. Implémenter la logique métier d'estimation
3. Ajouter la gestion des deux flux utilisateur
4. Intégrer une base de données
5. Développer les fonctionnalités premium

## Commandes utiles

```bash
# Développement
npm run dev          # Compilation des assets
npm run watch        # Compilation automatique
npm run serve        # Serveur PHP

# Production
npm run build        # Compilation optimisée
```
