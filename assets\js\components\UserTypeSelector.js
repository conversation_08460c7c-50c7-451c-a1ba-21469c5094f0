// Composant de sélection du type d'utilisateur
export const UserTypeSelector = {
    template: `
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
            <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">
                Qui êtes-vous ?
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button 
                    @click="selectUserType('freelance')"
                    :class="[
                        'p-6 border-2 rounded-lg transition-all duration-200 text-left',
                        selectedType === 'freelance' 
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                            : 'border-gray-200 dark:border-gray-600 hover:border-blue-300'
                    ]"
                >
                    <div class="flex items-center mb-2">
                        <span class="text-2xl mr-3">👨‍💻</span>
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white">
                            Développeur freelance
                        </h4>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        J'estime un projet pour un client ou pour évaluer ma charge de travail
                    </p>
                </button>
                
                <button 
                    @click="selectUserType('business')"
                    :class="[
                        'p-6 border-2 rounded-lg transition-all duration-200 text-left',
                        selectedType === 'business' 
                            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                            : 'border-gray-200 dark:border-gray-600 hover:border-blue-300'
                    ]"
                >
                    <div class="flex items-center mb-2">
                        <span class="text-2xl mr-3">🏢</span>
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white">
                            Entreprise ou client
                        </h4>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-400">
                        J'estime un projet pour mon entreprise ou pour budgétiser un développement
                    </p>
                </button>
            </div>
        </div>
    `,
    props: ['modelValue'],
    emits: ['update:modelValue'],
    computed: {
        selectedType() {
            return this.modelValue;
        }
    },
    methods: {
        selectUserType(type) {
            this.$emit('update:modelValue', type);
        }
    }
};
